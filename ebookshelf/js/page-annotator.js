// PageAnnotator React Component - Transpiled from JSX
// This component wraps LiterallyCanvas core functionality in a React component

"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireWildcard(require("react"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != _typeof(e) && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
// PageAnnotator React Component
// This component wraps LiterallyCanvas core functionality in a React component
var PageAnnotator = function PageAnnotator(_ref) {
  var _ref$width = _ref.width,
    width = _ref$width === void 0 ? 800 : _ref$width,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? 600 : _ref$height,
    _ref$tools = _ref.tools,
    tools = _ref$tools === void 0 ? null : _ref$tools,
    _ref$onDrawingChange = _ref.onDrawingChange,
    onDrawingChange = _ref$onDrawingChange === void 0 ? null : _ref$onDrawingChange,
    _ref$snapshot = _ref.snapshot,
    snapshot = _ref$snapshot === void 0 ? null : _ref$snapshot,
    _ref$backgroundShapes = _ref.backgroundShapes,
    backgroundShapes = _ref$backgroundShapes === void 0 ? [] : _ref$backgroundShapes,
    _ref$primaryColor = _ref.primaryColor,
    primaryColor = _ref$primaryColor === void 0 ? '#000' : _ref$primaryColor,
    _ref$secondaryColor = _ref.secondaryColor,
    secondaryColor = _ref$secondaryColor === void 0 ? '#fff' : _ref$secondaryColor,
    _ref$backgroundColor = _ref.backgroundColor,
    backgroundColor = _ref$backgroundColor === void 0 ? 'transparent' : _ref$backgroundColor,
    _ref$strokeWidths = _ref.strokeWidths,
    strokeWidths = _ref$strokeWidths === void 0 ? [1, 2, 5, 10, 20] : _ref$strokeWidths,
    _ref$defaultStrokeWid = _ref.defaultStrokeWidth,
    defaultStrokeWidth = _ref$defaultStrokeWid === void 0 ? 2 : _ref$defaultStrokeWid,
    _ref$imageSize = _ref.imageSize,
    imageSize = _ref$imageSize === void 0 ? null : _ref$imageSize,
    _ref$toolbarPosition = _ref.toolbarPosition,
    toolbarPosition = _ref$toolbarPosition === void 0 ? 'top' : _ref$toolbarPosition,
    _ref$onInit = _ref.onInit,
    onInit = _ref$onInit === void 0 ? null : _ref$onInit;
  var containerRef = (0, _react.useRef)(null);
  var lcRef = (0, _react.useRef)(null);
  var _useState = (0, _react.useState)(false),
    _useState2 = _slicedToArray(_useState, 2),
    isInitialized = _useState2[0],
    setIsInitialized = _useState2[1];
  var _useState3 = (0, _react.useState)('pen1'),
    _useState4 = _slicedToArray(_useState3, 2),
    activeTool = _useState4[0],
    setActiveTool = _useState4[1]; // Track active tool

  // Default tools if none provided
  var defaultTools = [LC.tools.Pencil, LC.tools.Eraser, LC.tools.Line, LC.tools.Rectangle, LC.tools.Ellipse, LC.tools.Text, LC.tools.Polygon, LC.tools.Pan, LC.tools.Eyedropper];
  (0, _react.useEffect)(function () {
    if (!containerRef.current || isInitialized) return;

    // Initialize LiterallyCanvas
    var lcOptions = {
      imageSize: imageSize || {
        width: width,
        height: height
      },
      tools: tools || defaultTools,
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      strokeWidths: strokeWidths,
      defaultStrokeWidth: defaultStrokeWidth,
      backgroundShapes: backgroundShapes,
      snapshot: snapshot
    };

    // Create LiterallyCanvas instance
    var lc = LC.init(containerRef.current, lcOptions);
    lcRef.current = lc;

    // Set up event listeners
    if (onDrawingChange) {
      lc.on('drawingChange', onDrawingChange);
    }
    if (onInit) {
      onInit(lc);
    }
    setIsInitialized(true);

    // Cleanup function
    return function () {
      if (lcRef.current) {
        lcRef.current.teardown();
        lcRef.current = null;
      }
      setIsInitialized(false);
    };
  }, []);

  // Update colors when props change
  (0, _react.useEffect)(function () {
    if (lcRef.current) {
      lcRef.current.setColor('primary', primaryColor);
    }
  }, [primaryColor]);
  (0, _react.useEffect)(function () {
    if (lcRef.current) {
      lcRef.current.setColor('secondary', secondaryColor);
    }
  }, [secondaryColor]);
  (0, _react.useEffect)(function () {
    if (lcRef.current) {
      lcRef.current.setColor('background', backgroundColor);
    }
  }, [backgroundColor]);

  // Expose LC instance methods
  var getSnapshot = function getSnapshot() {
    return lcRef.current ? lcRef.current.getSnapshot() : null;
  };
  var loadSnapshot = function loadSnapshot(snapshot) {
    if (lcRef.current) {
      lcRef.current.loadSnapshot(snapshot);
    }
  };
  var clear = function clear() {
    if (lcRef.current) {
      lcRef.current.clear();
    }
  };
  var undo = function undo() {
    if (lcRef.current) {
      lcRef.current.undo();
    }
  };
  var redo = function redo() {
    if (lcRef.current) {
      lcRef.current.redo();
    }
  };
  var setTool = function setTool(tool) {
    if (lcRef.current) {
      lcRef.current.setTool(new tool(lcRef.current));
    }
  };
  var setZoom = function setZoom(scale) {
    if (lcRef.current) {
      lcRef.current.setZoom(scale);
    }
  };
  var setPan = function setPan(x, y) {
    if (lcRef.current) {
      lcRef.current.setPan(x, y);
    }
  };
  var getImage = function getImage() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    return lcRef.current ? lcRef.current.getImage(options) : null;
  };
  var getSVGString = function getSVGString() {
    return lcRef.current ? lcRef.current.getSVGString() : '';
  };

  // Tool handlers
  var handleToolClick = function handleToolClick(toolName, toolClass, color, strokeWidth) {
    if (color === void 0) { color = null; }
    if (strokeWidth === void 0) { strokeWidth = null; }
    if (lcRef.current) {
      lcRef.current.setTool(new toolClass(lcRef.current));
      if (color) {
        lcRef.current.setColor('primary', color);
      }
      if (strokeWidth) {
        lcRef.current.setStrokeWidth(strokeWidth);
      }
      setActiveTool(toolName);
    }
  };

  var handleZoom = function handleZoom(direction) {
    if (lcRef.current) {
      var currentZoom = lcRef.current.getZoom();
      if (direction === 'in') {
        lcRef.current.setZoom(Math.min(currentZoom * 1.2, 3));
      } else {
        lcRef.current.setZoom(Math.max(currentZoom / 1.2, 0.5));
      }
    }
  };

  var handleUndo = function handleUndo() {
    if (lcRef.current) {
      lcRef.current.undo();
    }
  };

  var handleRedo = function handleRedo() {
    if (lcRef.current) {
      lcRef.current.redo();
    }
  };

  var handleClear = function handleClear() {
    if (lcRef.current && confirm('确定要清除所有绘画内容吗？')) {
      lcRef.current.clear();
    }
  };

  var handleExit = function handleExit() {
    if (window.closePageAnnotator) {
      window.closePageAnnotator();
    }
  };

  // Expose methods via ref
  _react.default.useImperativeHandle(/*#__PURE__*/_react.default.forwardRef(function () {}), function () {
    return {
      getSnapshot: getSnapshot,
      loadSnapshot: loadSnapshot,
      clear: clear,
      undo: undo,
      redo: redo,
      setTool: setTool,
      setZoom: setZoom,
      setPan: setPan,
      getImage: getImage,
      getSVGString: getSVGString,
      lc: lcRef.current
    };
  });
  return /*#__PURE__*/_react.default.createElement("div", {
    style: {
      position: 'relative',
      width: width + 'px',
      height: height + 'px'
    }
  }, /*#__PURE__*/_react.default.createElement("div", {
    ref: containerRef,
    style: {
      width: width + 'px',
      height: height + 'px',
      border: '1px solid #ccc',
      position: 'relative'
    },
    className: "literally-canvas-container"
  }), /*#__PURE__*/_react.default.createElement("div", {
    className: "page-annotator-toolbar",
    style: {
      position: 'absolute',
      top: '10px',
      right: '10px',
      display: 'flex',
      flexDirection: 'column',
      gap: '5px',
      zIndex: 1000
    }
  }, /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn pen1-btn" + (activeTool === 'pen1' ? ' active' : ''),
    onClick: function onClick() {
      handleToolClick('pen1', LC.tools.Pencil, '#ff0000', 3);
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/pen1.jpg)',
      backgroundSize: 'cover',
      border: activeTool === 'pen1' ? '3px solid #007bff' : 'none',
      cursor: 'pointer'
    },
    title: "\u7EA2\u7B14"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn pen2-btn" + (activeTool === 'pen2' ? ' active' : ''),
    onClick: function onClick() {
      handleToolClick('pen2', LC.tools.Pencil, '#0000ff', 3);
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/pen2.jpg)',
      backgroundSize: 'cover',
      border: activeTool === 'pen2' ? '3px solid #007bff' : 'none',
      cursor: 'pointer'
    },
    title: "\u84DD\u7B14"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn pen3-btn" + (activeTool === 'pen3' ? ' active' : ''),
    onClick: function onClick() {
      handleToolClick('pen3', LC.tools.Pencil, '#00ff00', 3);
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/pen3.jpg)',
      backgroundSize: 'cover',
      border: activeTool === 'pen3' ? '3px solid #007bff' : 'none',
      cursor: 'pointer'
    },
    title: "\u7EFF\u7B14"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn eraser-btn" + (activeTool === 'eraser' ? ' active' : ''),
    onClick: function onClick() {
      handleToolClick('eraser', LC.tools.Eraser);
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/eraser.jpg)',
      backgroundSize: 'cover',
      border: activeTool === 'eraser' ? '3px solid #007bff' : 'none',
      cursor: 'pointer'
    },
    title: "\u6A61\u76AE\u64E6"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn undo-btn",
    onClick: handleUndo,
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/undo.jpg)',
      backgroundSize: 'cover',
      border: 'none',
      cursor: 'pointer'
    },
    title: "\u64A4\u9500"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn redo-btn",
    onClick: handleRedo,
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/redo.jpg)',
      backgroundSize: 'cover',
      border: 'none',
      cursor: 'pointer'
    },
    title: "\u91CD\u505A"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn zoomin-btn",
    onClick: function onClick() {
      handleZoom('in');
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundColor: '#4CAF50',
      color: 'white',
      border: 'none',
      cursor: 'pointer',
      fontSize: '20px',
      fontWeight: 'bold'
    },
    title: "\u653E\u5927"
  }, "+"), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn zoomout-btn",
    onClick: function onClick() {
      handleZoom('out');
    },
    style: {
      width: '50px',
      height: '50px',
      backgroundColor: '#f44336',
      color: 'white',
      border: 'none',
      cursor: 'pointer',
      fontSize: '20px',
      fontWeight: 'bold'
    },
    title: "\u7F29\u5C0F"
  }, "-"), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn clear-btn",
    onClick: handleClear,
    style: {
      width: '50px',
      height: '50px',
      backgroundImage: 'url(../pics/clear.jpg)',
      backgroundSize: 'cover',
      border: 'none',
      cursor: 'pointer'
    },
    title: "\u6E05\u9664"
  }), /*#__PURE__*/_react.default.createElement("button", {
    className: "annotator-tool-btn exit-btn",
    onClick: handleExit,
    style: {
      width: '50px',
      height: '50px',
      backgroundColor: '#ff4444',
      color: 'white',
      border: 'none',
      cursor: 'pointer',
      fontSize: '24px',
      fontWeight: 'bold'
    },
    title: "\u9000\u51FA"
  }, "\u2715")));
};
var _default = exports.default = PageAnnotator;

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PageAnnotator;
}

// Make available globally
if (typeof window !== 'undefined') {
  window.PageAnnotator = PageAnnotator;
}
